import { getAuth } from 'firebase/auth';
import { app } from '@/lib/firebase';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8787';

class APIError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'APIError';
  }
}

async function getAuthToken(): Promise<string | null> {
  const auth = getAuth(app);
  const user = auth.currentUser;
  if (!user) {
    return null;
  }
  return user.getIdToken();
}

async function fetchWithAuth(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const token = await getAuthToken();
  const headers = new Headers(options.headers);
  
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    throw new APIError(
      response.status,
      `API request failed: ${response.statusText}`
    );
  }

  return response;
}

// Agent API Types
export interface AgentAnalysisRequest {
  code?: string;
  query: string;
  analysisType?: 'code-review' | 'bug-analysis' | 'optimization' | 'documentation' | 'general';
  context?: {
    fileName?: string;
    language?: string;
    projectType?: string;
  };
}

export interface AgentChatRequest {
  message: string;
  conversationId?: string;
  context?: {
    currentFile?: string;
    selectedCode?: string;
    projectContext?: string;
  };
}

export interface AgentResponse {
  id: string;
  response: string;
  timestamp: number;
  metadata?: {
    toolsUsed?: string[];
    executionTime?: number;
    tokensUsed?: number;
  };
}

export interface AgentStreamResponse {
  id: string;
  type: 'text' | 'tool_start' | 'tool_end' | 'error' | 'complete';
  content: string;
  timestamp: number;
  metadata?: {
    toolName?: string;
    toolInput?: any;
    toolOutput?: any;
  };
}

export interface AgentTool {
  name: string;
  description: string;
  server: string;
}

export interface AgentHealthStatus {
  environment: 'development' | 'production';
  agentStatus: 'ready' | 'not_initialized' | 'error';
  toolsCount: number;
  environmentValid: boolean;
  environmentErrors: string[];
  timestamp: number;
}

// Agent API Functions
export async function analyzeCode(request: AgentAnalysisRequest): Promise<AgentResponse> {
  const response = await fetchWithAuth('/api/v1/protected/agent/analyze', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  const result = await response.json();
  if (!result.success) {
    throw new Error(result.error || 'Analysis failed');
  }

  return result.data;
}

export async function chatWithAgent(request: AgentChatRequest): Promise<AgentResponse> {
  const response = await fetchWithAuth('/api/v1/protected/agent/chat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  const result = await response.json();
  if (!result.success) {
    throw new Error(result.error || 'Chat failed');
  }

  return result.data;
}

export async function getAvailableTools(): Promise<AgentTool[]> {
  const response = await fetchWithAuth('/api/v1/protected/agent/tools');
  const result = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || 'Failed to get tools');
  }

  return result.data.tools;
}

export async function getAgentHealth(): Promise<AgentHealthStatus> {
  const response = await fetchWithAuth('/api/v1/protected/agent/health');
  const result = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || 'Health check failed');
  }

  return result.data;
}

// Streaming Analysis
export async function* streamAnalysis(request: AgentAnalysisRequest): AsyncGenerator<AgentStreamResponse> {
  const token = await getAuthToken();
  
  const response = await fetch(`${API_BASE_URL}/api/v1/protected/agent/stream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new APIError(response.status, `Stream request failed: ${response.statusText}`);
  }

  if (!response.body) {
    throw new Error('No response body for streaming');
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  try {
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        break;
      }

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            yield data as AgentStreamResponse;
          } catch (error) {
            console.warn('Failed to parse SSE data:', line);
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

export { APIError };
