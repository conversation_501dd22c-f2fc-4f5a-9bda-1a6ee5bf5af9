import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Development MCP server configuration
 * Uses local MCP servers spawned as child processes
 */
export const devMCPConfig = {
  mcpServers: {
    filesystem: {
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-filesystem', process.cwd()],
      env: { 
        ALLOWED_EXTENSIONS: '.js,.ts,.tsx,.json,.md,.yml,.yaml,.env,.toml',
        MAX_FILE_SIZE: '1048576' // 1MB limit
      }
    },
    everything: {
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-everything'],
      env: {
        // Everything server provides web search, weather, and other tools
        // No specific environment variables needed for basic functionality
      }
    }
  },
  // Security settings for development
  disallowedTools: [
    // Restrict dangerous filesystem operations
    'filesystem_delete_file',
    'filesystem_delete_directory',
    // Restrict network operations that could be dangerous
    'everything_execute_command'
  ],
  // Agent configuration
  agentConfig: {
    maxSteps: 20,
    verbose: true,
    memoryEnabled: true,
    useServerManager: true,
    autoInitialize: true
  }
};

/**
 * Get MCP configuration with dynamic port allocation
 * @param {Object} availablePorts - Port assignments from port manager
 * @returns {Object} MCP configuration with port-specific settings
 */
export function getDevMCPConfig(availablePorts) {
  const config = { ...devMCPConfig };
  
  // Add port-specific configuration if needed
  // For development, we use child processes, so ports are auto-assigned
  // But we can use the allocated ports for HTTP-based servers if needed
  
  return config;
}

/**
 * Validate MCP configuration
 * @param {Object} config - MCP configuration to validate
 * @returns {boolean} True if configuration is valid
 */
export function validateMCPConfig(config) {
  if (!config || !config.mcpServers) {
    console.error('❌ Invalid MCP config: missing mcpServers');
    return false;
  }
  
  for (const [serverName, serverConfig] of Object.entries(config.mcpServers)) {
    if (!serverConfig.command && !serverConfig.url) {
      console.error(`❌ Invalid MCP server config for ${serverName}: missing command or url`);
      return false;
    }
  }
  
  return true;
}
