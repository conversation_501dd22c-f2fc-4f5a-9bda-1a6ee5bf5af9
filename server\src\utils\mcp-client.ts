import { MC<PERSON>lient } from 'mcp-use';
import type { MCPConfig } from '../config/mcp.js';
import type { AgentError, ValidationResult } from '../types/agent.js';

/**
 * MCP Client Manager
 * Handles MCP client lifecycle, connection management, and error recovery
 */
export class MCPClientManager {
  private client: MCPClient | null = null;
  private config: MCPConfig | null = null;
  private isInitialized = false;
  private connectionAttempts = 0;
  private maxRetries = 3;

  constructor(config: MCPConfig) {
    this.config = config;
  }

  /**
   * Initialize MCP client with configuration
   */
  async initialize(): Promise<void> {
    if (this.isInitialized && this.client) {
      return;
    }

    if (!this.config) {
      throw new Error('MCP configuration not provided');
    }

    try {
      this.client = new MCPClient(this.config);
      this.isInitialized = true;
      this.connectionAttempts = 0;
      
      console.log('✅ MCP Client initialized successfully');
    } catch (error) {
      this.connectionAttempts++;
      const agentError: AgentError = {
        code: 'MCP_INIT_FAILED',
        message: `Failed to initialize MCP client: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { attempt: this.connectionAttempts, error },
        timestamp: Date.now()
      };
      
      console.error('❌ MCP Client initialization failed:', agentError);
      
      if (this.connectionAttempts < this.maxRetries) {
        console.log(`🔄 Retrying MCP client initialization (${this.connectionAttempts}/${this.maxRetries})...`);
        await this.delay(1000 * this.connectionAttempts); // Exponential backoff
        return this.initialize();
      }
      
      throw agentError;
    }
  }

  /**
   * Get the MCP client instance
   */
  getClient(): MCPClient {
    if (!this.client || !this.isInitialized) {
      throw new Error('MCP client not initialized. Call initialize() first.');
    }
    return this.client;
  }

  /**
   * Check if client is ready for use
   */
  isReady(): boolean {
    return this.isInitialized && this.client !== null;
  }

  /**
   * Get available tools from all MCP servers
   */
  async getAvailableTools(): Promise<Array<{ name: string; description: string; server: string }>> {
    if (!this.isReady()) {
      throw new Error('MCP client not ready');
    }

    try {
      const tools = await this.client!.listTools();
      return tools.map(tool => ({
        name: tool.name,
        description: tool.description || 'No description available',
        server: tool.server || 'unknown'
      }));
    } catch (error) {
      console.error('❌ Failed to get available tools:', error);
      throw new Error(`Failed to get available tools: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate MCP server connections
   */
  async validateConnections(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!this.isReady()) {
      errors.push('MCP client not initialized');
      return { valid: false, errors, warnings };
    }

    try {
      // Test basic connectivity by listing tools
      await this.getAvailableTools();
    } catch (error) {
      errors.push(`MCP server connectivity test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Cleanup and close all MCP sessions
   */
  async cleanup(): Promise<void> {
    if (this.client) {
      try {
        await this.client.closeAllSessions();
        console.log('✅ MCP sessions closed successfully');
      } catch (error) {
        console.error('⚠️  Error closing MCP sessions:', error);
      }
    }
    
    this.client = null;
    this.isInitialized = false;
    this.connectionAttempts = 0;
  }

  /**
   * Reconnect to MCP servers
   */
  async reconnect(): Promise<void> {
    console.log('🔄 Reconnecting to MCP servers...');
    await this.cleanup();
    await this.initialize();
  }

  /**
   * Get client status information
   */
  getStatus(): {
    initialized: boolean;
    connectionAttempts: number;
    hasClient: boolean;
    configLoaded: boolean;
  } {
    return {
      initialized: this.isInitialized,
      connectionAttempts: this.connectionAttempts,
      hasClient: this.client !== null,
      configLoaded: this.config !== null
    };
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Create and initialize MCP client manager
 */
export async function createMCPClientManager(config: MCPConfig): Promise<MCPClientManager> {
  const manager = new MCPClientManager(config);
  await manager.initialize();
  return manager;
}

/**
 * Singleton instance for global use
 */
let globalMCPManager: MCPClientManager | null = null;

/**
 * Get or create global MCP client manager
 */
export async function getGlobalMCPManager(config?: MCPConfig): Promise<MCPClientManager> {
  if (!globalMCPManager && config) {
    globalMCPManager = await createMCPClientManager(config);
  }
  
  if (!globalMCPManager) {
    throw new Error('Global MCP manager not initialized and no config provided');
  }
  
  return globalMCPManager;
}

/**
 * Cleanup global MCP manager
 */
export async function cleanupGlobalMCPManager(): Promise<void> {
  if (globalMCPManager) {
    await globalMCPManager.cleanup();
    globalMCPManager = null;
  }
}
