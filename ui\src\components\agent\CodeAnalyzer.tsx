import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, Play, Square, RotateCcw, Code, AlertTriangle } from 'lucide-react';
import { useAgentStream } from '@/hooks/useAgentStream';
import type { AgentAnalysisRequest } from '@/services/agent-api';

const ANALYSIS_TYPES = [
  { value: 'general', label: 'General Analysis' },
  { value: 'code-review', label: 'Code Review' },
  { value: 'bug-analysis', label: 'Bug Analysis' },
  { value: 'optimization', label: 'Optimization' },
  { value: 'documentation', label: 'Documentation' },
] as const;

const LANGUAGES = [
  'typescript', 'javascript', 'python', 'java', 'csharp', 'cpp', 'rust', 'go', 'php', 'ruby'
];

export function CodeAnalyzer() {
  const [code, setCode] = useState('');
  const [query, setQuery] = useState('');
  const [analysisType, setAnalysisType] = useState<string>('general');
  const [fileName, setFileName] = useState('');
  const [language, setLanguage] = useState('');
  
  const { 
    isStreaming, 
    messages, 
    error, 
    isComplete, 
    startStream, 
    stopStream, 
    clearMessages, 
    retry 
  } = useAgentStream();

  const handleAnalyze = async () => {
    if (!query.trim()) {
      return;
    }

    const request: AgentAnalysisRequest = {
      query: query.trim(),
      code: code.trim() || undefined,
      analysisType: analysisType as any,
      context: {
        fileName: fileName.trim() || undefined,
        language: language || undefined,
        projectType: 'web-application'
      }
    };

    await startStream(request);
  };

  const handleStop = () => {
    stopStream();
  };

  const handleClear = () => {
    clearMessages();
    setCode('');
    setQuery('');
    setFileName('');
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'tool_start': return '🔧';
      case 'tool_end': return '✅';
      case 'error': return '❌';
      case 'complete': return '🎉';
      default: return '';
    }
  };

  const getMessageColor = (type: string) => {
    switch (type) {
      case 'tool_start': return 'text-blue-600';
      case 'tool_end': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'complete': return 'text-purple-600';
      default: return 'text-foreground';
    }
  };

  return (
    <div className="space-y-6">
      {/* Input Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Code className="w-5 h-5" />
            <span>Code Analysis</span>
          </CardTitle>
          <CardDescription>
            Analyze code with AI-powered insights and recommendations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Query Input */}
          <div className="space-y-2">
            <Label htmlFor="query">Analysis Query *</Label>
            <Textarea
              id="query"
              placeholder="What would you like to analyze? (e.g., 'Review this code for bugs', 'Optimize this function', 'Explain how this works')"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              rows={3}
            />
          </div>

          {/* Code Input */}
          <div className="space-y-2">
            <Label htmlFor="code">Code (Optional)</Label>
            <Textarea
              id="code"
              placeholder="Paste your code here for analysis..."
              value={code}
              onChange={(e) => setCode(e.target.value)}
              rows={8}
              className="font-mono text-sm"
            />
          </div>

          {/* Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="analysisType">Analysis Type</Label>
              <Select value={analysisType} onValueChange={setAnalysisType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ANALYSIS_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {LANGUAGES.map((lang) => (
                    <SelectItem key={lang} value={lang}>
                      {lang.charAt(0).toUpperCase() + lang.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fileName">File Name</Label>
              <Input
                id="fileName"
                placeholder="e.g., component.tsx"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <Button 
              onClick={handleAnalyze} 
              disabled={!query.trim() || isStreaming}
              className="flex items-center space-x-2"
            >
              {isStreaming ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              <span>{isStreaming ? 'Analyzing...' : 'Analyze'}</span>
            </Button>

            {isStreaming && (
              <Button variant="outline" onClick={handleStop}>
                <Square className="w-4 h-4 mr-2" />
                Stop
              </Button>
            )}

            {error && (
              <Button variant="outline" onClick={retry}>
                <RotateCcw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            )}

            <Button variant="ghost" onClick={handleClear}>
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {(messages.length > 0 || error) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Analysis Results</span>
              <div className="flex items-center space-x-2">
                {isStreaming && (
                  <Badge variant="secondary" className="animate-pulse">
                    Streaming...
                  </Badge>
                )}
                {isComplete && (
                  <Badge variant="default">
                    Complete
                  </Badge>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertTriangle className="w-4 h-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {messages.map((message, index) => (
                <div
                  key={message.id || index}
                  className={`p-3 rounded-lg border ${getMessageColor(message.type)}`}
                >
                  <div className="flex items-start space-x-2">
                    <span className="text-lg">{getMessageIcon(message.type)}</span>
                    <div className="flex-1">
                      {message.type !== 'text' && (
                        <div className="text-sm font-medium mb-1">
                          {message.type.replace('_', ' ').toUpperCase()}
                          {message.metadata?.toolName && ` - ${message.metadata.toolName}`}
                        </div>
                      )}
                      <div className="whitespace-pre-wrap text-sm">
                        {message.content}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
