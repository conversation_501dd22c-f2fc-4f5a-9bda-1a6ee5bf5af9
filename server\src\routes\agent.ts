import { Hono } from 'hono';
import { 
  analyze<PERSON><PERSON><PERSON><PERSON><PERSON>,
  streamAnalysisHandler,
  chat<PERSON><PERSON><PERSON>,
  getToolsH<PERSON><PERSON>,
  healthCheckHandler
} from '../controllers/agent.js';

/**
 * Agent API Routes
 * All routes are protected and require authentication
 */
const agentRoutes = new Hono();

/**
 * POST /agent/analyze
 * Analyze code with AI agent
 * 
 * Body:
 * {
 *   "query": "string",
 *   "code": "string (optional)",
 *   "analysisType": "code-review | bug-analysis | optimization | documentation | general",
 *   "context": {
 *     "fileName": "string (optional)",
 *     "language": "string (optional)",
 *     "projectType": "string (optional)"
 *   }
 * }
 */
agentRoutes.post('/analyze', analyzeCodeHandler);

/**
 * POST /agent/stream
 * Stream analysis with real-time updates
 * 
 * Body: Same as /analyze
 * Response: Server-Sent Events stream
 */
agentRoutes.post('/stream', streamAnalysisHandler);

/**
 * POST /agent/chat
 * Interactive chat with AI agent
 * 
 * Body:
 * {
 *   "message": "string",
 *   "conversationId": "string (optional)",
 *   "context": {
 *     "currentFile": "string (optional)",
 *     "selectedCode": "string (optional)",
 *     "projectContext": "string (optional)"
 *   }
 * }
 */
agentRoutes.post('/chat', chatHandler);

/**
 * GET /agent/tools
 * Get available MCP tools
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "tools": [
 *       {
 *         "name": "string",
 *         "description": "string",
 *         "server": "string"
 *       }
 *     ],
 *     "count": number,
 *     "environment": "development | production"
 *   }
 * }
 */
agentRoutes.get('/tools', getToolsHandler);

/**
 * GET /agent/health
 * Health check for agent service
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "environment": "development | production",
 *     "agentStatus": "ready | not_initialized | error",
 *     "toolsCount": number,
 *     "environmentValid": boolean,
 *     "environmentErrors": string[],
 *     "timestamp": number
 *   }
 * }
 */
agentRoutes.get('/health', healthCheckHandler);

export default agentRoutes;
