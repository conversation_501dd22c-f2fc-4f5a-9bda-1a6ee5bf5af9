# 🪄 Design System 

## 🎨 Color Tokens

### Light Theme
```css
:root {
  --background: #f5f5ff;
  --foreground: #2a2a4a;
  --card: #ffffff;
  --card-foreground: #2a2a4a;
  --popover: #ffffff;
  --popover-foreground: #2a2a4a;
  --primary: #6e56cf;
  --primary-foreground: #ffffff;
  --secondary: #e4dfff;
  --secondary-foreground: #4a4080;
  --muted: #f0f0fa;
  --muted-foreground: #6c6c8a;
  --accent: #d8e6ff;
  --accent-foreground: #2a2a4a;
  --destructive: #ff5470;
  --destructive-foreground: #ffffff;
  --border: #e0e0f0;
  --input: #e0e0f0;
  --ring: #6e56cf;

  /* Charts */
  --chart-1: #6e56cf;
  --chart-2: #9e8cfc;
  --chart-3: #5d5fef;
  --chart-4: #7c75fa;
  --chart-5: #4740b3;

  /* Sidebar */
  --sidebar: #f0f0fa;
  --sidebar-foreground: #2a2a4a;
  --sidebar-primary: #6e56cf;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #d8e6ff;
  --sidebar-accent-foreground: #2a2a4a;
  --sidebar-border: #e0e0f0;
  --sidebar-ring: #6e56cf;
}
```

### Dark Theme
```css
.dark {
  --background: #0f0f1a;
  --foreground: #e2e2f5;
  --card: #1a1a2e;
  --card-foreground: #e2e2f5;
  --popover: #1a1a2e;
  --popover-foreground: #e2e2f5;
  --primary: #a48fff;
  --primary-foreground: #0f0f1a;
  --secondary: #2d2b55;
  --secondary-foreground: #c4c2ff;
  --muted: #222244;
  --muted-foreground: #a0a0c0;
  --accent: #303060;
  --accent-foreground: #e2e2f5;
  --destructive: #ff5470;
  --destructive-foreground: #ffffff;
  --border: #303052;
  --input: #303052;
  --ring: #a48fff;

  /* Charts */
  --chart-1: #a48fff;
  --chart-2: #7986cb;
  --chart-3: #64b5f6;
  --chart-4: #4db6ac;
  --chart-5: #ff79c6;

  /* Sidebar */
  --sidebar: #1a1a2e;
  --sidebar-foreground: #e2e2f5;
  --sidebar-primary: #a48fff;
  --sidebar-primary-foreground: #0f0f1a;
  --sidebar-accent: #303060;
  --sidebar-accent-foreground: #e2e2f5;
  --sidebar-border: #303052;
  --sidebar-ring: #a48fff;
}
```

## 🔡 Typography

### Font Families
DM Serif Display
Roboto Mono

### Font Sizes + Line Heights (8pt rhythm)
| Token         | Font Size | Line Height | Use Case        |
|---------------|-----------|-------------|-----------------|
| --font-xs     | 12px      | 16px        | Labels/Captions |
| --font-sm     | 14px      | 20px        | UI Labels       |
| --font-base   | 16px      | 24px        | Body Text       |
| --font-md     | 18px      | 28px        | Paragraphs      |
| --font-lg     | 20px      | 32px        | H5              |
| --font-xl     | 24px      | 40px        | H4              |
| --font-2xl    | 32px      | 48px        | H3              |
| --font-3xl    | 40px      | 56px        | H2              |
| --font-4xl    | 48px      | 64px        | H1 / Hero       |
```
shadows
:root {
  --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs:  0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm:  0px 4px 10px 0px hsl(240 30% 25% / 0.12),
                0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow:     0px 4px 10px 0px hsl(240 30% 25% / 0.12),
                0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md:  0px 4px 10px 0px hsl(240 30% 25% / 0.12),
                0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg:  0px 4px 10px 0px hsl(240 30% 25% / 0.12),
                0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl:  0px 4px 10px 0px hsl(240 30% 25% / 0.12),
                0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
}




spacing
| Token        | Value |
| ------------ | ----- |
| `--space-0`  | 0px   |
| `--space-1`  | 8px   |
| `--space-2`  | 16px  |
| `--space-3`  | 24px  |
| `--space-4`  | 32px  |
| `--space-5`  | 40px  |
| `--space-6`  | 48px  |
| `--space-7`  | 56px  |
| `--space-8`  | 64px  |
| `--space-9`  | 72px  |
| `--space-10` | 80px  |
