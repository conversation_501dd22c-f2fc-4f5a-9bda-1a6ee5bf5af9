import { useState, useCallback, useRef } from 'react';
import { streamAnalysis, type AgentAnalysisRequest, type AgentStreamResponse } from '@/services/agent-api';

export interface UseAgentStreamState {
  isStreaming: boolean;
  messages: AgentStreamResponse[];
  error: string | null;
  isComplete: boolean;
}

export interface UseAgentStreamActions {
  startStream: (request: AgentAnalysisRequest) => Promise<void>;
  stopStream: () => void;
  clearMessages: () => void;
  retry: () => Promise<void>;
}

export function useAgentStream(): UseAgentStreamState & UseAgentStreamActions {
  const [state, setState] = useState<UseAgentStreamState>({
    isStreaming: false,
    messages: [],
    error: null,
    isComplete: false,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const lastRequestRef = useRef<AgentAnalysisRequest | null>(null);

  const startStream = useCallback(async (request: AgentAnalysisRequest) => {
    // Store the request for retry functionality
    lastRequestRef.current = request;

    // Cancel any existing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      isStreaming: true,
      messages: [],
      error: null,
      isComplete: false,
    }));

    try {
      const streamGenerator = streamAnalysis(request);
      
      for await (const message of streamGenerator) {
        // Check if stream was aborted
        if (abortControllerRef.current?.signal.aborted) {
          break;
        }

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, message],
          isComplete: message.type === 'complete',
        }));

        // If we received a completion or error, stop streaming
        if (message.type === 'complete' || message.type === 'error') {
          setState(prev => ({
            ...prev,
            isStreaming: false,
            isComplete: true,
          }));
          break;
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Stream was intentionally cancelled
        setState(prev => ({
          ...prev,
          isStreaming: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          isStreaming: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred',
          isComplete: true,
        }));
      }
    }
  }, []);

  const stopStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isStreaming: false,
    }));
  }, []);

  const clearMessages = useCallback(() => {
    setState(prev => ({
      ...prev,
      messages: [],
      error: null,
      isComplete: false,
    }));
  }, []);

  const retry = useCallback(async () => {
    if (lastRequestRef.current) {
      await startStream(lastRequestRef.current);
    }
  }, [startStream]);

  return {
    ...state,
    startStream,
    stopStream,
    clearMessages,
    retry,
  };
}
