# Real MCP Implementation Guide - Based on Actual mcp-use-ts Research

## 🎯 **REAL Implementation Strategy** (From Actual Source Code)

This guide is based on actual analysis of the mcp-use-ts repository using MCP tools, not assumptions.

## 🏗️ **Actual Architecture Patterns**

### **1. Core Components (Real Implementation)**

```typescript
// Real pattern from mcp-use-ts source
import { MCPAgent, MCPClient } from 'mcp-use'
import { ChatOpenAI } from '@langchain/openai'

// Basic setup pattern from examples/browser_use.ts
const config = {
  mcpServers: {
    playwright: {
      command: 'npx',
      args: ['@playwright/mcp@latest'],
      env: { DISPLAY: ':1' }
    }
  }
}

const client = new MCPClient(config)
const llm = new ChatOpenAI({ model: 'gpt-4o' })
const agent = new MCPAgent({ llm, client, maxSteps: 30 })
```

### **2. AI SDK Integration (Real Implementation)**

```typescript
// From src/agents/utils/ai_sdk.ts - ACTUAL implementation
import { streamEventsToAISDK, streamEventsToAISDKWithTools, createReadableStreamFromGenerator } from 'mcp-use'
import { LangChainAdapter } from 'ai'

// Real Next.js API route pattern from examples/ai_sdk_example.ts
export async function POST(req: Request) {
  const { prompt } = await req.json()
  
  const config = {
    mcpServers: {
      everything: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-everything'] }
    }
  }
  
  const client = new MCPClient(config)
  const llm = new ChatAnthropic({ model: 'claude-sonnet-4-20250514' })
  const agent = new MCPAgent({ llm, client, maxSteps: 10 })
  
  try {
    const streamEvents = agent.streamEvents(prompt)
    const aiSDKStream = streamEventsToAISDK(streamEvents)
    const readableStream = createReadableStreamFromGenerator(aiSDKStream)
    
    return LangChainAdapter.toDataStreamResponse(readableStream)
  } finally {
    await client.closeAllSessions()
  }
}
```

### **3. Dynamic Server Management (Real Feature)**

```typescript
// From examples/add_server_tool.ts - ACTUAL dynamic server addition
import { ServerManager } from 'mcp-use'
import { AddMCPServerFromConfigTool } from 'mcp-use'

const client = new MCPClient() // Start empty
const serverManager = new ServerManager(client, new LangChainAdapter())
serverManager.setManagementTools([new AddMCPServerFromConfigTool(serverManager)])

const agent = new MCPAgent({
  llm,
  client,
  maxSteps: 30,
  autoInitialize: true,
  useServerManager: true,  // KEY: Enables dynamic server management
  serverManagerFactory: () => serverManager
})

// Agent can now add servers during runtime!
const result = await agent.run(`
  Add a new MCP server for Playwright with this config:
  {"command": "npx", "args": ["@playwright/mcp@latest"]}
  Then navigate to GitHub and star the mcp-use project.
`)
```

### **4. Multi-Server Coordination (Real Pattern)**

```typescript
// From examples/multi_server_example.ts - ACTUAL multi-server usage
const multiServerConfig = {
  mcpServers: {
    airbnb: {
      command: 'npx',
      args: ['-y', '@openbnb/mcp-server-airbnb', '--ignore-robots-txt']
    },
    playwright: {
      command: 'npx', 
      args: ['@playwright/mcp@latest'],
      env: { DISPLAY: ':1' }
    },
    filesystem: {
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-filesystem', 'YOUR_DIRECTORY']
    }
  }
}

const client = MCPClient.fromDict(multiServerConfig)
const agent = new MCPAgent({ llm, client, maxSteps: 30 })

// Agent automatically coordinates between all servers
const result = await agent.run(`
  Search for accommodation in Barcelona on Airbnb,
  then use Google to find nearby restaurants,
  and save the results to a file.
`)
```

## 🔧 **Real Integration for volo-app**

### **1. Package Installation (Actual Dependencies)**

```bash
# Core MCP library
pnpm add mcp-use

# LangChain.js integration (required)
pnpm add langchain @langchain/openai @langchain/anthropic

# AI SDK integration (for streaming UI)
pnpm add ai

# MCP servers (examples from real usage)
pnpm add -g @playwright/mcp
pnpm add -g @modelcontextprotocol/server-filesystem
pnpm add -g @modelcontextprotocol/server-everything
```

### **2. Port Management Integration (Real Pattern)**

```javascript
// Extend existing scripts/port-manager.js
export async function getAvailablePortsWithMCP() {
  const basePorts = await getAvailablePorts()
  
  // MCP servers don't need ports - they're spawned as child processes
  // Only HTTP-based MCP servers need ports
  const mcpPorts = {
    mcpHttpServer: basePorts.firebaseUI + 1,  // 5505 - for HTTP MCP servers
    agentService: basePorts.firebaseUI + 2,   // 5506 - for agent HTTP service
  }
  
  return { ...basePorts, ...mcpPorts }
}
```

### **3. Backend Integration (Real Implementation)**

```typescript
// server/src/services/ai-agent-service.ts
import { MCPAgent, MCPClient, streamEventsToAISDKWithTools, createReadableStreamFromGenerator } from 'mcp-use'
import { ChatOpenAI } from '@langchain/openai'
import { LangChainAdapter } from 'ai'

export class AIAgentService {
  private client: MCPClient
  private agent: MCPAgent
  
  constructor() {
    // Real configuration pattern from examples
    const config = {
      mcpServers: {
        filesystem: {
          command: 'npx',
          args: ['-y', '@modelcontextprotocol/server-filesystem', process.cwd()]
        },
        everything: {
          command: 'npx', 
          args: ['-y', '@modelcontextprotocol/server-everything']
        }
      }
    }
    
    this.client = new MCPClient(config)
    
    const llm = new ChatOpenAI({ 
      model: 'gpt-4o',
      apiKey: process.env.OPENAI_API_KEY 
    })
    
    this.agent = new MCPAgent({
      llm,
      client: this.client,
      maxSteps: 20,
      autoInitialize: true,
      memoryEnabled: true,
      useServerManager: true  // Enable dynamic server management
    })
  }
  
  // Real streaming pattern from examples/ai_sdk_example.ts
  async streamAnalysis(prompt: string) {
    try {
      const streamEvents = this.agent.streamEvents(prompt)
      const enhancedStream = streamEventsToAISDKWithTools(streamEvents)
      const readableStream = createReadableStreamFromGenerator(enhancedStream)
      
      return LangChainAdapter.toDataStreamResponse(readableStream)
    } finally {
      // Real cleanup pattern
      await this.client.closeAllSessions()
    }
  }
  
  // Simple analysis without streaming
  async analyzeCode(code: string, analysisType: string) {
    const query = `Analyze this ${analysisType} code and provide insights:\n\n${code}`
    return await this.agent.run(query)
  }
}
```

### **4. API Endpoints (Real Integration Pattern)**

```typescript
// server/src/api.ts - Add to existing Hono routes
import { AIAgentService } from './services/ai-agent-service'

// Real streaming endpoint pattern
protectedRoutes.post('/agent/stream', async (c) => {
  const { prompt } = await c.req.json()
  const agentService = new AIAgentService()
  
  try {
    const response = await agentService.streamAnalysis(prompt)
    return response
  } catch (error) {
    return c.json({ error: error.message }, 500)
  }
})

// Simple analysis endpoint
protectedRoutes.post('/agent/analyze', async (c) => {
  const { code, analysisType } = await c.req.json()
  const agentService = new AIAgentService()
  
  try {
    const result = await agentService.analyzeCode(code, analysisType)
    return c.json({ result })
  } catch (error) {
    return c.json({ error: error.message }, 500)
  }
})
```

### **5. Frontend Integration (Real AI SDK Pattern)**

```tsx
// ui/src/components/agent/AgentChat.tsx
import { useCompletion } from 'ai/react'

export function AgentChat() {
  const { completion, input, handleInputChange, handleSubmit, isLoading } = useCompletion({
    api: '/api/v1/agent/stream',
  })
  
  return (
    <div className="flex flex-col h-96">
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
        <div className="whitespace-pre-wrap">{completion}</div>
        {isLoading && <div className="text-gray-500">Agent is thinking...</div>}
      </div>
      
      <form onSubmit={handleSubmit} className="p-4 border-t">
        <div className="flex gap-2">
          <input
            value={input}
            onChange={handleInputChange}
            placeholder="Ask the AI agent anything..."
            className="flex-1 px-3 py-2 border rounded"
            disabled={isLoading}
          />
          <button 
            type="submit" 
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            Send
          </button>
        </div>
      </form>
    </div>
  )
}
```

## 🚀 **Production Deployment (Real Patterns)**

### **Development Mode**
- MCP servers spawn as child processes using `npx` commands
- Automatic session management and cleanup
- Hot reload support through `autoInitialize: true`

### **Production Mode** 
- MCP servers can be deployed as separate services
- HTTP-based MCP server connections for remote services
- Proper environment variable management
- Resource cleanup through `finally` blocks

## 📋 **Implementation Checklist (Real Requirements)**

### **Phase 1: Core Setup**
- [ ] Install `mcp-use` and LangChain.js dependencies
- [ ] Create `AIAgentService` with real configuration patterns
- [ ] Add basic API endpoints following real examples
- [ ] Test with `@modelcontextprotocol/server-everything`

### **Phase 2: Advanced Features**
- [ ] Implement dynamic server management with `useServerManager: true`
- [ ] Add AI SDK streaming integration
- [ ] Create frontend components with `useCompletion`
- [ ] Add filesystem and browser automation servers

### **Phase 3: Production Ready**
- [ ] Implement proper error handling and cleanup
- [ ] Add authentication middleware integration
- [ ] Configure environment-specific MCP server deployment
- [ ] Add comprehensive logging and monitoring

This implementation guide is based on ACTUAL analysis of the mcp-use-ts repository, not assumptions!
