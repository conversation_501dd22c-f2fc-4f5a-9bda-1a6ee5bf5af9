import type { StreamEvent } from 'mcp-use';

export interface AgentAnalysisRequest {
  code?: string;
  query: string;
  analysisType?: 'code-review' | 'bug-analysis' | 'optimization' | 'documentation' | 'general';
  context?: {
    fileName?: string;
    language?: string;
    projectType?: string;
  };
}

export interface AgentChatRequest {
  message: string;
  conversationId?: string;
  context?: {
    currentFile?: string;
    selectedCode?: string;
    projectContext?: string;
  };
}

export interface AgentResponse {
  id: string;
  response: string;
  timestamp: number;
  metadata?: {
    toolsUsed?: string[];
    executionTime?: number;
    tokensUsed?: number;
  };
}

export interface AgentStreamResponse {
  id: string;
  type: 'text' | 'tool_start' | 'tool_end' | 'error' | 'complete';
  content: string;
  timestamp: number;
  metadata?: {
    toolName?: string;
    toolInput?: any;
    toolOutput?: any;
  };
}

export interface AgentToolInfo {
  name: string;
  description: string;
  parameters?: Record<string, any>;
  server: string;
}

export interface AgentServiceConfig {
  openaiApiKey: string;
  model: string;
  maxSteps: number;
  verbose: boolean;
  memoryEnabled: boolean;
  useServerManager: boolean;
}

export interface AgentSession {
  id: string;
  userId?: string;
  createdAt: number;
  lastActivity: number;
  conversationHistory: AgentMessage[];
}

export interface AgentMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  metadata?: {
    toolsUsed?: string[];
    executionTime?: number;
  };
}

export interface AgentError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

// Stream event types for better type safety
export type AgentStreamEvent = StreamEvent;

// Analysis result types
export interface CodeAnalysisResult {
  summary: string;
  issues: Array<{
    type: 'error' | 'warning' | 'suggestion';
    message: string;
    line?: number;
    severity: 'low' | 'medium' | 'high';
  }>;
  suggestions: string[];
  metrics?: {
    complexity?: number;
    maintainability?: number;
    testCoverage?: number;
  };
}

export interface ProjectInsight {
  type: 'architecture' | 'dependencies' | 'performance' | 'security';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  actionItems?: string[];
}

// Configuration validation types
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

// Export utility types
export type AgentAnalysisType = AgentAnalysisRequest['analysisType'];
export type AgentStreamEventType = AgentStreamResponse['type'];
export type AgentMessageRole = AgentMessage['role'];
