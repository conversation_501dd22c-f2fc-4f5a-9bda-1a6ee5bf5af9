import { MCPAgent } from 'mcp-use';
import { ChatOpenAI } from '@langchain/openai';
import type { 
  AgentAnalysisRequest, 
  AgentChatRequest, 
  AgentResponse, 
  AgentStreamResponse,
  AgentServiceConfig,
  AgentSession,
  AgentMessage,
  AgentError,
  CodeAnalysisResult,
  ProjectInsight
} from '../types/agent.js';
import { MCPClientManager } from '../utils/mcp-client.js';
import type { MCPConfig } from '../config/mcp.js';

/**
 * AI Agent Service
 * Core service for AI agent functionality using mcp-use-ts and OpenAI
 */
export class AIAgentService {
  private agent: MCPAgent | null = null;
  private mcpManager: MCPClientManager;
  private llm: ChatOpenAI;
  private config: AgentServiceConfig;
  private sessions: Map<string, AgentSession> = new Map();

  constructor(mcpConfig: MCPConfig, serviceConfig: AgentServiceConfig) {
    this.config = serviceConfig;
    this.mcpManager = new MCPClientManager(mcpConfig);
    
    // Initialize OpenAI LLM
    this.llm = new ChatOpenAI({
      modelName: serviceConfig.model,
      apiKey: serviceConfig.openaiApiKey,
      temperature: 0.1,
      maxTokens: 4000
    });
  }

  /**
   * Initialize the AI agent service
   */
  async initialize(): Promise<void> {
    try {
      // Initialize MCP client manager
      await this.mcpManager.initialize();
      
      // Create MCP agent
      this.agent = new MCPAgent({
        llm: this.llm,
        client: this.mcpManager.getClient(),
        maxSteps: this.config.maxSteps,
        verbose: this.config.verbose,
        memoryEnabled: this.config.memoryEnabled,
        useServerManager: this.config.useServerManager,
        autoInitialize: true
      });

      console.log('✅ AI Agent Service initialized successfully');
    } catch (error) {
      const agentError: AgentError = {
        code: 'AGENT_INIT_FAILED',
        message: `Failed to initialize AI agent: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error,
        timestamp: Date.now()
      };
      
      console.error('❌ AI Agent initialization failed:', agentError);
      throw agentError;
    }
  }

  /**
   * Analyze code with AI agent
   */
  async analyzeCode(request: AgentAnalysisRequest): Promise<AgentResponse> {
    if (!this.agent) {
      throw new Error('AI Agent not initialized');
    }

    const startTime = Date.now();
    
    try {
      // Build analysis prompt based on request type
      const prompt = this.buildAnalysisPrompt(request);
      
      // Run agent analysis
      const result = await this.agent.run(prompt);
      
      const response: AgentResponse = {
        id: this.generateId(),
        response: result,
        timestamp: Date.now(),
        metadata: {
          executionTime: Date.now() - startTime,
          toolsUsed: [] // TODO: Extract from agent execution
        }
      };

      return response;
    } catch (error) {
      throw this.createAgentError('ANALYSIS_FAILED', error);
    }
  }

  /**
   * Stream analysis with real-time updates
   */
  async *streamAnalysis(request: AgentAnalysisRequest): AsyncGenerator<AgentStreamResponse> {
    if (!this.agent) {
      throw new Error('AI Agent not initialized');
    }

    try {
      const prompt = this.buildAnalysisPrompt(request);
      const streamEvents = this.agent.streamEvents(prompt);
      
      for await (const event of streamEvents) {
        const streamResponse: AgentStreamResponse = {
          id: this.generateId(),
          type: this.mapEventType(event.event),
          content: this.extractEventContent(event),
          timestamp: Date.now(),
          metadata: this.extractEventMetadata(event)
        };
        
        yield streamResponse;
      }
    } catch (error) {
      yield {
        id: this.generateId(),
        type: 'error',
        content: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Interactive chat with AI agent
   */
  async chat(request: AgentChatRequest): Promise<AgentResponse> {
    if (!this.agent) {
      throw new Error('AI Agent not initialized');
    }

    const startTime = Date.now();
    
    try {
      // Get or create session
      const session = this.getOrCreateSession(request.conversationId);
      
      // Build chat prompt with context
      const prompt = this.buildChatPrompt(request, session);
      
      // Run agent
      const result = await this.agent.run(prompt);
      
      // Update session
      this.updateSession(session, request.message, result);
      
      const response: AgentResponse = {
        id: this.generateId(),
        response: result,
        timestamp: Date.now(),
        metadata: {
          executionTime: Date.now() - startTime
        }
      };

      return response;
    } catch (error) {
      throw this.createAgentError('CHAT_FAILED', error);
    }
  }

  /**
   * Get available tools from MCP servers
   */
  async getAvailableTools() {
    return await this.mcpManager.getAvailableTools();
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.mcpManager.cleanup();
    this.sessions.clear();
    this.agent = null;
  }

  /**
   * Build analysis prompt based on request
   */
  private buildAnalysisPrompt(request: AgentAnalysisRequest): string {
    let prompt = `Please analyze the following ${request.analysisType || 'code'}:\n\n`;
    
    if (request.code) {
      prompt += `Code:\n\`\`\`${request.context?.language || ''}\n${request.code}\n\`\`\`\n\n`;
    }
    
    prompt += `Query: ${request.query}\n\n`;
    
    if (request.context) {
      prompt += `Context:\n`;
      if (request.context.fileName) prompt += `- File: ${request.context.fileName}\n`;
      if (request.context.language) prompt += `- Language: ${request.context.language}\n`;
      if (request.context.projectType) prompt += `- Project Type: ${request.context.projectType}\n`;
    }
    
    // Add analysis type specific instructions
    switch (request.analysisType) {
      case 'code-review':
        prompt += `\nPlease provide a thorough code review focusing on:\n- Code quality and best practices\n- Potential bugs or issues\n- Performance considerations\n- Security concerns\n- Maintainability`;
        break;
      case 'bug-analysis':
        prompt += `\nPlease analyze for potential bugs and issues:\n- Logic errors\n- Edge cases\n- Error handling\n- Type safety\n- Runtime issues`;
        break;
      case 'optimization':
        prompt += `\nPlease suggest optimizations for:\n- Performance improvements\n- Memory usage\n- Algorithm efficiency\n- Code structure\n- Resource utilization`;
        break;
      case 'documentation':
        prompt += `\nPlease help with documentation:\n- Generate clear documentation\n- Explain complex logic\n- Add helpful comments\n- Create usage examples`;
        break;
    }
    
    return prompt;
  }

  /**
   * Build chat prompt with session context
   */
  private buildChatPrompt(request: AgentChatRequest, session: AgentSession): string {
    let prompt = '';
    
    // Add conversation history (last 5 messages)
    const recentMessages = session.conversationHistory.slice(-5);
    if (recentMessages.length > 0) {
      prompt += 'Previous conversation:\n';
      recentMessages.forEach(msg => {
        prompt += `${msg.role}: ${msg.content}\n`;
      });
      prompt += '\n';
    }
    
    // Add current context
    if (request.context) {
      prompt += 'Current context:\n';
      if (request.context.currentFile) prompt += `- Current file: ${request.context.currentFile}\n`;
      if (request.context.selectedCode) prompt += `- Selected code:\n\`\`\`\n${request.context.selectedCode}\n\`\`\`\n`;
      if (request.context.projectContext) prompt += `- Project context: ${request.context.projectContext}\n`;
      prompt += '\n';
    }
    
    prompt += `User: ${request.message}`;
    
    return prompt;
  }

  // Helper methods
  private generateId(): string {
    return `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private mapEventType(eventType: string): AgentStreamResponse['type'] {
    switch (eventType) {
      case 'on_tool_start': return 'tool_start';
      case 'on_tool_end': return 'tool_end';
      case 'on_chat_model_stream': return 'text';
      default: return 'text';
    }
  }

  private extractEventContent(event: any): string {
    if (event.event === 'on_chat_model_stream' && event.data?.chunk?.text) {
      return event.data.chunk.text;
    }
    if (event.event === 'on_tool_start') {
      return `🔧 Using tool: ${event.name}`;
    }
    if (event.event === 'on_tool_end') {
      return `✅ Tool completed: ${event.name}`;
    }
    return '';
  }

  private extractEventMetadata(event: any): any {
    return {
      toolName: event.name,
      toolInput: event.data?.input,
      toolOutput: event.data?.output
    };
  }

  private getOrCreateSession(conversationId?: string): AgentSession {
    const sessionId = conversationId || this.generateId();
    
    if (!this.sessions.has(sessionId)) {
      const session: AgentSession = {
        id: sessionId,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        conversationHistory: []
      };
      this.sessions.set(sessionId, session);
    }
    
    return this.sessions.get(sessionId)!;
  }

  private updateSession(session: AgentSession, userMessage: string, agentResponse: string): void {
    const timestamp = Date.now();
    
    session.conversationHistory.push({
      id: this.generateId(),
      role: 'user',
      content: userMessage,
      timestamp
    });
    
    session.conversationHistory.push({
      id: this.generateId(),
      role: 'assistant',
      content: agentResponse,
      timestamp
    });
    
    session.lastActivity = timestamp;
  }

  private createAgentError(code: string, error: any): AgentError {
    return {
      code,
      message: error instanceof Error ? error.message : 'Unknown error',
      details: error,
      timestamp: Date.now()
    };
  }
}
