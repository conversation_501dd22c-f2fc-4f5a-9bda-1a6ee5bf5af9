import { Context } from 'hono';
import { AIAgentService } from '../services/ai-agent.js';
import { getMCPConfig, getEnvironment, validateEnvironment } from '../config/mcp.js';
import type { 
  AgentAnalysisRequest, 
  AgentChatRequest, 
  AgentServiceConfig,
  AgentError 
} from '../types/agent.js';

// Global agent service instance
let globalAgentService: AIAgentService | null = null;

/**
 * Get or create global agent service instance
 */
async function getAgentService(): Promise<AIAgentService> {
  if (!globalAgentService) {
    // Validate environment first
    const envValidation = validateEnvironment();
    if (!envValidation.valid) {
      throw new Error(`Environment validation failed: ${envValidation.errors.join(', ')}`);
    }

    // Get MCP configuration
    const environment = getEnvironment();
    const mcpConfig = getMCPConfig(environment);
    
    // Create service configuration
    const serviceConfig: AgentServiceConfig = {
      openaiApiKey: process.env.OPENAI_API_KEY!,
      model: 'gpt-4o',
      maxSteps: environment === 'production' ? 15 : 20,
      verbose: environment === 'development',
      memoryEnabled: true,
      useServerManager: environment === 'development'
    };

    // Create and initialize agent service
    globalAgentService = new AIAgentService(mcpConfig, serviceConfig);
    await globalAgentService.initialize();
    
    console.log(`✅ AI Agent Service initialized for ${environment} environment`);
  }

  return globalAgentService;
}

/**
 * Handle code analysis requests
 */
export async function analyzeCodeHandler(c: Context) {
  try {
    const user = c.get('user');
    const request: AgentAnalysisRequest = await c.req.json();
    
    // Validate request
    if (!request.query) {
      return c.json({ error: 'Query is required' }, 400);
    }

    const agentService = await getAgentService();
    const result = await agentService.analyzeCode(request);
    
    return c.json({
      success: true,
      data: result,
      userId: user.id
    });
  } catch (error) {
    console.error('Code analysis error:', error);
    
    const agentError = error as AgentError;
    return c.json({
      success: false,
      error: agentError.message || 'Analysis failed',
      code: agentError.code || 'ANALYSIS_ERROR',
      timestamp: Date.now()
    }, 500);
  }
}

/**
 * Handle streaming analysis requests
 */
export async function streamAnalysisHandler(c: Context) {
  try {
    const user = c.get('user');
    const request: AgentAnalysisRequest = await c.req.json();
    
    // Validate request
    if (!request.query) {
      return c.json({ error: 'Query is required' }, 400);
    }

    const agentService = await getAgentService();
    
    // Set up Server-Sent Events
    return c.streamText(async (stream) => {
      try {
        for await (const event of agentService.streamAnalysis(request)) {
          await stream.write(`data: ${JSON.stringify(event)}\n\n`);
        }
        
        // Send completion event
        await stream.write(`data: ${JSON.stringify({
          id: `completion_${Date.now()}`,
          type: 'complete',
          content: 'Analysis completed',
          timestamp: Date.now()
        })}\n\n`);
      } catch (streamError) {
        console.error('Streaming error:', streamError);
        await stream.write(`data: ${JSON.stringify({
          id: `error_${Date.now()}`,
          type: 'error',
          content: `Streaming failed: ${streamError instanceof Error ? streamError.message : 'Unknown error'}`,
          timestamp: Date.now()
        })}\n\n`);
      }
    }, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  } catch (error) {
    console.error('Stream setup error:', error);
    return c.json({
      success: false,
      error: 'Failed to setup streaming',
      timestamp: Date.now()
    }, 500);
  }
}

/**
 * Handle chat requests
 */
export async function chatHandler(c: Context) {
  try {
    const user = c.get('user');
    const request: AgentChatRequest = await c.req.json();
    
    // Validate request
    if (!request.message) {
      return c.json({ error: 'Message is required' }, 400);
    }

    const agentService = await getAgentService();
    const result = await agentService.chat(request);
    
    return c.json({
      success: true,
      data: result,
      userId: user.id
    });
  } catch (error) {
    console.error('Chat error:', error);
    
    const agentError = error as AgentError;
    return c.json({
      success: false,
      error: agentError.message || 'Chat failed',
      code: agentError.code || 'CHAT_ERROR',
      timestamp: Date.now()
    }, 500);
  }
}

/**
 * Get available MCP tools
 */
export async function getToolsHandler(c: Context) {
  try {
    const agentService = await getAgentService();
    const tools = await agentService.getAvailableTools();
    
    return c.json({
      success: true,
      data: {
        tools,
        count: tools.length,
        environment: getEnvironment()
      }
    });
  } catch (error) {
    console.error('Get tools error:', error);
    return c.json({
      success: false,
      error: 'Failed to get available tools',
      timestamp: Date.now()
    }, 500);
  }
}

/**
 * Health check for agent service
 */
export async function healthCheckHandler(c: Context) {
  try {
    const environment = getEnvironment();
    const envValidation = validateEnvironment();
    
    let agentStatus = 'not_initialized';
    let toolsCount = 0;
    
    try {
      if (globalAgentService) {
        const tools = await globalAgentService.getAvailableTools();
        agentStatus = 'ready';
        toolsCount = tools.length;
      }
    } catch (error) {
      agentStatus = 'error';
    }
    
    return c.json({
      success: true,
      data: {
        environment,
        agentStatus,
        toolsCount,
        environmentValid: envValidation.valid,
        environmentErrors: envValidation.errors,
        timestamp: Date.now()
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    return c.json({
      success: false,
      error: 'Health check failed',
      timestamp: Date.now()
    }, 500);
  }
}

/**
 * Cleanup agent service (for graceful shutdown)
 */
export async function cleanupAgentService(): Promise<void> {
  if (globalAgentService) {
    await globalAgentService.cleanup();
    globalAgentService = null;
    console.log('✅ Agent service cleaned up');
  }
}
