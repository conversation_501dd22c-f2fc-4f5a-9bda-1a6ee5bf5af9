/**
 * Production MCP server configuration
 * Uses HTTP-based MCP servers for Cloudflare Workers compatibility
 */
export const prodMCPConfig = {
  mcpServers: {
    // Use HTTP-based MCP servers for production
    // These would be deployed separately and accessed via HTTP
    filesystem: {
      url: process.env.MCP_FILESYSTEM_URL || 'https://mcp-filesystem.your-domain.com'
    },
    everything: {
      url: process.env.MCP_EVERYTHING_URL || 'https://mcp-everything.your-domain.com'
    }
  },
  // Stricter security settings for production
  disallowedTools: [
    // Restrict all filesystem write operations in production
    'filesystem_write_file',
    'filesystem_delete_file',
    'filesystem_delete_directory',
    'filesystem_create_directory',
    // Restrict dangerous network operations
    'everything_execute_command',
    'everything_system_info'
  ],
  // Agent configuration for production
  agentConfig: {
    maxSteps: 15, // Lower limit for production
    verbose: false, // Disable verbose logging
    memoryEnabled: true,
    useServerManager: false, // Disable dynamic server management
    autoInitialize: true
  }
};

/**
 * Get production MCP configuration with environment-specific settings
 * @param {Object} env - Environment variables
 * @returns {Object} MCP configuration for production
 */
export function getProdMCPConfig(env = process.env) {
  const config = { ...prodMCPConfig };
  
  // Override URLs from environment variables
  if (env.MCP_FILESYSTEM_URL) {
    config.mcpServers.filesystem.url = env.MCP_FILESYSTEM_URL;
  }
  
  if (env.MCP_EVERYTHING_URL) {
    config.mcpServers.everything.url = env.MCP_EVERYTHING_URL;
  }
  
  // Add authentication headers if provided
  const authToken = env.MCP_AUTH_TOKEN;
  if (authToken) {
    Object.values(config.mcpServers).forEach(server => {
      if (server.url) {
        server.headers = {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        };
      }
    });
  }
  
  return config;
}

/**
 * Validate production MCP configuration
 * @param {Object} config - MCP configuration to validate
 * @param {Object} env - Environment variables
 * @returns {Object} Validation result with errors if any
 */
export function validateProdMCPConfig(config, env = process.env) {
  const errors = [];
  
  if (!config || !config.mcpServers) {
    errors.push('Missing mcpServers configuration');
    return { valid: false, errors };
  }
  
  // Check that all servers have valid URLs
  for (const [serverName, serverConfig] of Object.entries(config.mcpServers)) {
    if (!serverConfig.url) {
      errors.push(`Missing URL for MCP server: ${serverName}`);
    } else {
      try {
        new URL(serverConfig.url);
      } catch (e) {
        errors.push(`Invalid URL for MCP server ${serverName}: ${serverConfig.url}`);
      }
    }
  }
  
  // Check required environment variables
  if (!env.OPENAI_API_KEY) {
    errors.push('Missing required environment variable: OPENAI_API_KEY');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}
