import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Bot, Code, MessageSquare, Wrench } from 'lucide-react';
import { CodeAnalyzer } from './CodeAnalyzer';
import { StreamingChat } from './StreamingChat';
import { getAgentHealth, getAvailableTools, type AgentHealthStatus, type AgentTool } from '@/services/agent-api';

export function AgentInterface() {
  const [health, setHealth] = useState<AgentHealthStatus | null>(null);
  const [tools, setTools] = useState<AgentTool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAgentData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [healthData, toolsData] = await Promise.all([
          getAgentHealth(),
          getAvailableTools()
        ]);

        setHealth(healthData);
        setTools(toolsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load agent data');
      } finally {
        setLoading(false);
      }
    };

    loadAgentData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'bg-green-500';
      case 'not_initialized': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading AI Agent...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to initialize AI Agent: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Bot className="w-8 h-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">AI Agent Assistant</h1>
            <p className="text-muted-foreground">
              Intelligent code analysis and development companion
            </p>
          </div>
        </div>
        
        {health && (
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(health.agentStatus)}`} />
            <Badge variant="outline">
              {health.environment} • {health.agentStatus}
            </Badge>
          </div>
        )}
      </div>

      {/* Status Card */}
      {health && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Wrench className="w-5 h-5" />
              <span>Agent Status</span>
            </CardTitle>
            <CardDescription>
              Current status and available tools
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium">Environment</p>
                <p className="text-2xl font-bold capitalize">{health.environment}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Status</p>
                <p className="text-2xl font-bold capitalize">{health.agentStatus.replace('_', ' ')}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Available Tools</p>
                <p className="text-2xl font-bold">{health.toolsCount}</p>
              </div>
            </div>
            
            {!health.environmentValid && health.environmentErrors.length > 0 && (
              <Alert variant="destructive" className="mt-4">
                <AlertDescription>
                  Environment issues: {health.environmentErrors.join(', ')}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Main Interface */}
      <Tabs defaultValue="analyzer" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="analyzer" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>Code Analyzer</span>
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center space-x-2">
            <MessageSquare className="w-4 h-4" />
            <span>AI Chat</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="analyzer" className="space-y-4">
          <CodeAnalyzer />
        </TabsContent>

        <TabsContent value="chat" className="space-y-4">
          <StreamingChat />
        </TabsContent>
      </Tabs>

      {/* Available Tools */}
      {tools.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Available Tools</CardTitle>
            <CardDescription>
              MCP tools available for the AI agent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tools.map((tool, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{tool.name}</h4>
                    <Badge variant="secondary" className="text-xs">
                      {tool.server}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {tool.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
