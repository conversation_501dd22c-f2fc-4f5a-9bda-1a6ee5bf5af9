import { devMCPConfig, getDevMCPConfig, validateMCPConfig } from '../../../scripts/mcp-config.dev.js';
import { prodMCPConfig, getProdMCPConfig, validateProdMCPConfig } from '../../../scripts/mcp-config.prod.js';

export interface MCPServerConfig {
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  url?: string;
  headers?: Record<string, string>;
}

export interface MCPConfig {
  mcpServers: Record<string, MCPServerConfig>;
  disallowedTools?: string[];
  agentConfig: {
    maxSteps: number;
    verbose: boolean;
    memoryEnabled: boolean;
    useServerManager: boolean;
    autoInitialize: boolean;
  };
}

export interface PortAllocation {
  backend: number;
  frontend: number;
  postgres: number;
  firebaseAuth: number;
  firebaseUI: number;
  mcpFilesystem: number;
  mcpEverything: number;
  agentService: number;
}

/**
 * Get MCP configuration based on environment
 * @param env - Environment (development or production)
 * @param availablePorts - Port allocation from port manager
 * @returns MCP configuration object
 */
export function getMCPConfig(
  env: 'development' | 'production' = 'development',
  availablePorts?: PortAllocation
): MCPConfig {
  if (env === 'production') {
    const config = getProdMCPConfig(process.env);
    const validation = validateProdMCPConfig(config, process.env);
    
    if (!validation.valid) {
      throw new Error(`Invalid production MCP config: ${validation.errors.join(', ')}`);
    }
    
    return config;
  } else {
    const config = getDevMCPConfig(availablePorts || {} as PortAllocation);
    
    if (!validateMCPConfig(config)) {
      throw new Error('Invalid development MCP config');
    }
    
    return config;
  }
}

/**
 * Get environment-specific configuration
 */
export function getEnvironment(): 'development' | 'production' {
  const nodeEnv = process.env.NODE_ENV;
  
  // Check if running in Cloudflare Workers
  if (typeof globalThis.caches !== 'undefined' && typeof globalThis.Request !== 'undefined') {
    return 'production';
  }
  
  return nodeEnv === 'production' ? 'production' : 'development';
}

/**
 * Validate that required environment variables are present
 */
export function validateEnvironment(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const env = getEnvironment();
  
  // Check OpenAI API key
  if (!process.env.OPENAI_API_KEY) {
    errors.push('Missing required environment variable: OPENAI_API_KEY');
  }
  
  // Production-specific validations
  if (env === 'production') {
    // In production, we might need MCP server URLs
    if (!process.env.MCP_FILESYSTEM_URL && !process.env.MCP_EVERYTHING_URL) {
      console.warn('⚠️  No MCP server URLs configured for production. Using defaults.');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Default export for easy importing
 */
export default {
  getMCPConfig,
  getEnvironment,
  validateEnvironment,
  devMCPConfig,
  prodMCPConfig
};
